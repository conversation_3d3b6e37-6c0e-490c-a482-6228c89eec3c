"""
飞书查询处理器模块
负责协调整个查询处理流程
"""
import asyncio
from src.utils.logger import logger
from src.services.feishu.message_apis import (
    reply_simple_text_message,
    send_updates_to_card,
    send_finished_message_to_card,
)
from .conversation_service import ConversationService
from .agent_service import AgentService
from .stream_processor import StreamProcessor
from .card_service import CardService
from .config import FeishuConfig


class QueryProcessor:
    """查询处理器"""
    
    @staticmethod
    async def handle_agent_query(
        message_id: str, user_query: str, user_info_dict: dict, root_id: str = None
    ):
        """处理用户查询并将更新流式传输到飞书卡片
        
        Args:
            message_id: 消息ID
            user_query: 用户查询
            user_info_dict: 用户信息字典
            root_id: 根消息ID
        """
        sequence = 0
        card_id = None
        element_id = None
        
        try:
            user_name = user_info_dict.get("name", "unknown_user")
            user_id = user_info_dict.get("open_id", "unknown_user")
            user_email = user_info_dict.get("email", "unknown")
            
            logger.info(f"开始处理用户 {user_name} 的查询: {user_query}")
            
            # 使用root_id作为conversation_id，如果没有则使用message_id
            conversation_id = root_id if root_id else message_id
            
            # 1. 验证对话所有权
            if not await ConversationService.validate_conversation_ownership(
                user_name, user_email, message_id, root_id
            ):
                return
            
            # 2. 尽早创建初始卡片，让用户快速收到响应
            card_id, element_id = await CardService.create_initial_card_early(
                message_id, user_query, conversation_id, user_id
            )
            if not card_id:
                return
            
            # 3. 保存用户消息到历史记录
            await ConversationService.save_user_message_to_history(
                user_name, user_email, conversation_id, user_query
            )
            
            # 4. 获取对话历史记录
            history = await ConversationService.get_conversation_history(
                user_name, user_email, conversation_id, root_id
            )
            
            # 5. 初始化Agent
            model_name = None  # 默认使用OPENAI_MODEL
            agent, user_info_obj = await AgentService.initialize_agent_and_user_info(
                user_info_dict, model_name
            )
            
            # 6. 流式处理Agent响应
            (
                last_sequence,
                full_response,
                full_log_message,
                structured_assistant_message,
                timeouted,
            ) = await StreamProcessor.process_agent_stream(
                agent, user_query, user_info_obj, card_id, sequence, history
            )
            
            # 7. 处理超时情况
            if timeouted:
                card_id, element_id, sequence = await QueryProcessor._handle_timeout(
                    message_id, user_query, conversation_id, user_id, 
                    card_id, full_response, element_id
                )
            else:
                sequence = last_sequence
            
            # 8. 完成卡片
            await CardService.finish_card(card_id, sequence, conversation_id)
            
            # 9. 保存助手回复到历史记录
            await ConversationService.save_assistant_response_to_history(
                user_name, user_email, conversation_id, full_response,
                full_log_message, structured_assistant_message
            )
            
            logger.info(f"查询处理完成，最终回复长度: {len(full_response)}")
            
        except Exception as e:
            await QueryProcessor._handle_error(
                e, message_id, card_id, element_id, sequence
            )
    
    @staticmethod
    async def _handle_timeout(
        message_id: str, user_query: str, conversation_id: str, user_id: str,
        old_card_id: str, full_response: str, element_id: str
    ) -> tuple[str, str, int]:
        """处理超时情况
        
        Returns:
            tuple: (card_id, element_id, sequence)
        """
        logger.warning("检测到流式响应超时，创建新卡片并发送完整响应")
        
        # 创建新卡片
        new_card_id, new_element_id = await CardService.create_timeout_card(
            message_id, user_query, conversation_id, user_id
        )
        
        if not new_card_id:
            return old_card_id, element_id, 0
        
        # 发送完整响应到新卡片
        sequence = 0  # 重置序列号
        sequence = await asyncio.to_thread(
            send_updates_to_card,
            card_id=new_card_id,
            markdown_content=full_response,
            element_id=new_element_id,
            sequence=sequence,
        )
        
        return new_card_id, new_element_id, sequence
    
    @staticmethod
    async def _handle_error(
        error, message_id, card_id=None, element_id=None, sequence=0
    ):
        """处理查询过程中的错误
        
        Args:
            error: 错误对象
            message_id: 消息ID
            card_id: 卡片ID
            element_id: 元素ID
            sequence: 序列号
        """
        logger.error(f"处理Agent查询时出错: {error}", exc_info=True)
        
        # 尝试发送错误消息到卡片，如果可能的话，否则回复文本
        try:
            error_message = f"处理您的请求时发生内部错误。\n错误信息: {error}"
            if card_id:
                error_sequence = sequence + 1
                await asyncio.to_thread(
                    send_updates_to_card,
                    card_id,
                    error_message,
                    element_id,
                    sequence=error_sequence,
                )
                finish_sequence = error_sequence + 1
                await asyncio.to_thread(
                    send_finished_message_to_card,
                    card_id,
                    sequence=finish_sequence,
                    chat_id="invalid_chat_id",
                )
            else:
                reply_simple_text_message(message_id, error_message)
        except Exception as send_error:
            logger.error(f"发送错误消息到飞书时再次出错: {send_error}")
            reply_simple_text_message(
                message_id, "处理您的请求时发生内部错误，并且无法更新卡片状态。"
            )
