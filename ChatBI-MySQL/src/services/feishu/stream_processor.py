"""
飞书流式处理器模块
负责处理Agent的流式响应并更新卡片
"""
import time
import asyncio
from agents import Runner, RunConfig
from services.agent.utils.model_provider import CUSTOM_MODEL_PROVIDER
from src.services.feishu.agent_message_formatter import extract_content_from_stream_event
from src.utils.logger import logger
from .config import FeishuConfig
from .card_service import CardService
from .agent_service import AgentService


class StreamProcessor:
    """流式响应处理器"""
    
    @staticmethod
    async def process_agent_stream(
        agent,
        user_query,
        user_info_obj,
        card_id,
        initial_sequence: int,
        history=None,
    ) -> tuple[int, str, str, dict, bool | None]:
        """处理Agent的流式响应并更新卡片
        
        Args:
            agent: Agent实例
            user_query: 用户查询
            user_info_obj: 用户信息对象
            card_id: 卡片ID
            initial_sequence: 初始序列号
            history: 历史记录
            
        Returns:
            tuple: (last_sequence, full_response, full_log_message, structured_assistant_message, timeouted)
        """
        current_sequence = initial_sequence
        full_response = ""
        full_log_message = ""
        last_sent_length = 0
        accumulated_content = ""
        retry_count = 0
        max_retries, retry_interval = FeishuConfig.get_retry_config()
        
        # 记录流式响应的开始时间
        start_time = time.time()
        timeout_minutes = FeishuConfig.get_stream_timeout_minutes()
        logger.info(f"流式响应开始时间: {start_time}, 超时时间设置: {timeout_minutes}分钟")
        
        # 准备输入消息
        input_messages = AgentService.prepare_input_messages(history, user_query)
        
        timeouted = False
        
        while True:  # 添加循环以支持重试
            result = Runner.run_streamed(
                agent,
                input=input_messages,
                run_config=RunConfig(model_provider=CUSTOM_MODEL_PROVIDER),
                context=user_info_obj,
            )
            
            # 重置响应变量（如果是重试）
            if retry_count > 0:
                full_response = ""
                full_log_message = ""
                last_sent_length = 0
                accumulated_content = ""
                logger.info(f"重试次数: {retry_count}")
            
            async for item in result.stream_events():
                content_chunk, log_message = extract_content_from_stream_event(item)
                
                # 记录调试信息
                if log_message:
                    logger.debug(f"Stream Event ({item.type}): {log_message}")
                    full_log_message += log_message
                
                # 更新卡片内容
                if content_chunk:
                    full_response += content_chunk
                    accumulated_content += content_chunk
                    
                    # 检查累积内容是否比上次发送的内容长度超过MESSAGE_STEPS个字符
                    if len(accumulated_content) > FeishuConfig.get_message_steps():
                        # 检查是否已经超时
                        elapsed_minutes = (time.time() - start_time) / 60
                        if elapsed_minutes >= timeout_minutes:
                            logger.warning(
                                f"流式响应已超时 ({elapsed_minutes:.2f}分钟 >= {timeout_minutes}分钟)，停止发送更新"
                            )
                            # 表示发生了超时异常
                            timeouted = True
                            continue
                        
                        current_sequence += 1
                        # 发送累积的响应到卡片
                        current_sequence = await CardService.send_thinking_and_reply_updates(
                            card_id, full_response, current_sequence
                        )
                        last_sent_length = len(full_response)
                        accumulated_content = ""  # 重置累积内容
            
            # 流式响应结束后，确保发送完整的最终响应
            if current_sequence == 0:
                logger.warning(f"流式响应超时，未发送最终主内容更新\n:{full_response}")
            elif full_response and (len(full_response) > last_sent_length):
                logger.info(
                    f"发送最终主内容更新 {current_sequence} 到卡片 {card_id} (总内容长度: {len(full_response)})"
                )
                # 强制发送思考过程，确保最终响应完整
                current_sequence = await CardService.send_thinking_and_reply_updates(
                    card_id, full_response, current_sequence, force_send_thinking=True
                )
            
            # 检查是否包含Handoff日志，如果不包含且未达到最大重试次数，则重试
            if "Handoff from " not in full_log_message and retry_count < max_retries:
                retry_count += 1
                current_sequence = await CardService.handle_retry_logic(
                    card_id, current_sequence, retry_count, retry_interval
                )
                # 继续循环，重新运行AI
                continue
            else:
                # 如果包含Handoff日志或已达到最大重试次数，则跳出循环
                if retry_count > 0:
                    logger.info(
                        f"重试完成，{'检测到Handoff日志' if 'Handoff from ' in full_log_message else '达到最大重试次数'}"
                    )
                break
        
        # 尝试提取结构化的助手消息
        structured_assistant_message = AgentService.extract_structured_assistant_message(result)
        
        return (
            current_sequence,
            full_response,
            full_log_message,
            structured_assistant_message,
            timeouted,
        )
